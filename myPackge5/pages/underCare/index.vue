<template>
    <view>
        <CustomNavbar :bgColor="'#08BA7E'" :titleColor="'#FFFFFF'" />
        <view class="header">
            <view class="fifter">
                <img src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/xiaoshouhetong/shaixuan.png"
                    alt="" @click="fifterClick" />
            </view>
        </view>

        <view class="tab-container">
            <scroll-view class="tabs-scroll" scroll-x="true" show-scrollbar="false" :scroll-left="scrollLeft"
                scroll-with-animation="true" :scroll-into-view="scrollIntoView">
                <view class="tabs">
                    <view class="tab-item" :class="{ 'active': currentTab === 1 }" @click="switchTab(1)" :id="'tab-1'">
                        <view class="icon-box" :class="{ 'active': currentTab === 1 }">
                            <image src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/mine/siyang.png"
                                class="tab-icon" />
                        </view>
                        饲养管理
                    </view>
                    <view class="tab-item" :class="{ 'active': currentTab === 2 }" @click="switchTab(2)" :id="'tab-2'">
                        <view class="icon-box" :class="{ 'active': currentTab === 2 }">
                            <image src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/mine/shengzhang.png"
                                class="tab-icon" />
                        </view>
                        生长监测
                    </view>
                    <view class="tab-item" :class="{ 'active': currentTab === 3 }" @click="switchTab(3)" :id="'tab-3'">
                        <view class="icon-box" :class="{ 'active': currentTab === 3 }">
                            <image src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/mine/jibing.png"
                                class="tab-icon" />
                        </view>
                        疾病防控
                    </view>
                    <view class="tab-item" :class="{ 'active': currentTab === 4 }" @click="switchTab(4)" :id="'tab-4'">
                        <view class="icon-box" :class="{ 'active': currentTab === 4 }">
                            <image src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/mine/richang.png"
                                class="tab-icon" />
                        </view>
                        日常记录
                    </view>
                </view>
            </scroll-view>
        </view>

        <view class="tab-content">
            <FeedingManagement v-if="currentTab === 1" />
            <GrowthMonitoring v-if="currentTab === 2" />
            <DiseaseControl v-if="currentTab === 3" />
            <DailyRecord v-if="currentTab === 4" />
        </view>
        <filterPopup @resetSearch="resetSearch" :pickerFilterShow="pickerFilterShow" @canel="pickerFilterShow = false"
            @submitForm="submitForm" />
    </view>
</template>

<script>
import filterPopup from '@/components/filterPopup/index.vue'
import CustomNavbar from '@/components/uni-custom-navbar/CustomNavbar.vue'
import FeedingManagement from './components/FeedingManagement.vue'
import GrowthMonitoring from './components/GrowthMonitoring.vue'
import DiseaseControl from './components/DiseaseControl.vue'
import DailyRecord from './components/DailyRecord.vue'

export default {
    components: {
        CustomNavbar,
        filterPopup,
        FeedingManagement,
        GrowthMonitoring,
        DiseaseControl,
        DailyRecord
    },
    data() {
        return {
            isIphonex: getApp().globalData.systemInfo.isIphonex,
            pickerFilterShow: false,
            filters: {},
            list: [],
            currentTab: 1, // 当前选中的tab
            scrollLeft: 0, // tab滚动位置
            scrollIntoView: '', // 滚动到指定元素
        }
    },
    onLoad() {
    },
    onUnload() {
    },
    onShow() { },
    computed: {

    },
    methods: {
        switchTab(tabIndex) {
            console.log('切换到tab:', tabIndex);
            this.currentTab = tabIndex;
            this.$nextTick(() => {
                this.scrollToTab(tabIndex);
            });
        },

        // 滚动到指定tab
        scrollToTab(tabIndex) {
            console.log('开始滚动到tab:', tabIndex);

            // 计算目标滚动位置
            const systemInfo = uni.getSystemInfoSync();
            const screenWidth = systemInfo.screenWidth;

            // 每个tab的宽度和间距（rpx转px）
            const tabWidthPx = (188 / 750) * screenWidth;
            const tabMarginPx = (20 / 750) * screenWidth;

            // 根据tab索引计算目标滚动位置
            let targetScrollLeft = 0;

            if (tabIndex === 1 || tabIndex === 2) {
                targetScrollLeft = 0;
            } else if (tabIndex === 3) {
                // 滚动一个tab的距离
                targetScrollLeft = tabWidthPx + tabMarginPx;
            } else if (tabIndex === 4) {
                // 滚动两个tab的距离，让第4个tab完全可见
                targetScrollLeft = (tabWidthPx + tabMarginPx) * 2;
            }

            console.log('当前滚动位置:', this.scrollLeft, '目标滚动位置:', targetScrollLeft);

            // 只有当目标位置与当前位置不同时才执行滚动
            if (Math.abs(targetScrollLeft - this.scrollLeft) > 1) {
                console.log('需要滚动，执行滚动操作');
                this.scrollIntoView = `tab-${tabIndex}`;
                setTimeout(() => {
                    // 更新滚动位置
                    this.scrollLeft = targetScrollLeft;
                    // 清除scroll-into-view
                    setTimeout(() => {
                        this.scrollIntoView = '';
                    }, 100);
                }, 50);
            } else {
            }
        },
        // 搜索
        fifterClick() {
            this.pickerFilterShow = true
        },

        resetSearch() {
        },
        submitForm(val) {
            console.log(val)
            this.pickerFilterShow = false
        },
    },
}
</script>

<style lang="scss" scoped>
.header {
    width: 750rpx;
    height: 727rpx;
    display: flex;
    padding-top: 120rpx;
    /* 导航栏空间 */
    box-sizing: border-box;
    position: relative;
    background: url(https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/cow/bg.png) no-repeat 100% 100%;
    background-size: 100% 100%;
    position: relative;
}

.fifter {
    position: absolute;
    top: 195rpx;
    right: 30rpx;

    img {
        width: 34rpx;
        height: 32.5rpx;
    }
}

.tab-container {
    margin-top: -372rpx;
    padding: 0 26rpx;
    margin-bottom: 30rpx;
}

.tabs-scroll {
    width: 100%;
    white-space: nowrap;
}

.tabs {
    display: flex;
    align-items: center;
    width: max-content;
    padding: 0 10rpx;
}

.tab-item {
    width: 188rpx;
    height: 65rpx;
    background: #FFFFFF;
    border-radius: 32rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #1DB17A;
    font-weight: 500;
    font-size: 26rpx;
    position: relative;
    transition: all 0.3s ease;
    margin-right: 17rpx;
    flex-shrink: 0;
    padding: 5rpx 20rpx 5rpx 5rpx;

    .icon-box {
        width: 50rpx;
        height: 50rpx;
        background-color: #E3FFEE;
        border-radius: 50%;
        margin-right: 10rpx;
        padding: 12rpx;
        box-sizing: border-box;

        &.active {
            background-color: #ffffff !important;
        }

        .tab-icon {
            width: 25rpx;
            height: 25rpx;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    }

    &:last-child {
        margin-right: 10rpx;
    }

    &.active {
        color: #FFFFFF;
        background: linear-gradient(140deg, #1CC271 0%, #5CD26F 100%);

        // 气泡效果
        &::before {
            content: '';
            position: absolute;
            bottom: -8rpx;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 0;
            border-left: 8rpx solid transparent;
            border-right: 8rpx solid transparent;
            border-top: 8rpx solid #5CD26F;
        }
    }
}

.tab-content {
    padding: 0 30rpx;
    height: calc(100vh - 500rpx);
    overflow: hidden;
}

.main {
    margin-top: -372rpx;
}

.Add {
    width: 152rpx;
    height: 145rpx;
    position: absolute;
    bottom: 290rpx;
    right: 10rpx;

    img {
        width: 152rpx;
        height: 145rpx;
    }
}
</style>
